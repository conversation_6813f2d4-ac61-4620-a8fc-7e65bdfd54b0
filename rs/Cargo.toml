[package]
name = "mt-thyrios"
version = "0.1.0"
description = "A colony survival sim set on a gradually flooding mountain."
edition = "2024"
authors = ["Waridley"]
rust-version = "1.86.0"

[features]
dev_tools = [
    "dep:bevy-inspector-egui",
    "dep:bevy_console",
    "dep:clap",
    "dep:disqualified",
    "dep:egui-snarl",
    "dep:glob",
    "dep:smol",
    "bevy/asset_processor",
    "bevy/file_watcher",
]
track_changes = ["bevy/track_location"]
skip_splash = []

[dependencies.bevy]
version = "0.16.0-rc"

[dependencies]
bevy_egui = { version = "0.35.0", features = ["serde"] }
#bevy_hanabi = "0.16.0"
#bevy-steamworks = { version = "0.13.0", git = "https://github.com/eero-lehtinen/bevy_steamworks.git", branch = "bevy-16" }
leafwing-input-manager = { version = "0.17.1" }

bytemuck = "1.20.0"
enum-map = { version = "3.0.0-beta.2", features = ["serde"] }
egui_colors = "0.8.0"
gltf = "1.4.1"
gltf-json = "1.4.1"
nutype = "0.6.1"
rand = "0.8.5"
serde = "1"
strum = { version = "0.26.3", features = ["derive"] }
smolset = "1"
thiserror = "2.0.11"
tiny_bail = { version = "0.4.3", default-features = false, features = ["tracing", "error"] }

# Dev tools
bevy-inspector-egui = { version = "0.32.0", optional = true }
bevy_console = { package = "waridley_bevy_console", version = "0.13.1", path = "../vend/bevy-console", optional = true }
clap = { version = "4.5", optional = true, features = ["cargo"] }
disqualified = { version = "1.0.0", optional = true }
egui-snarl = { version = "0.7.1", optional = true }
glob = { version = "0.3.2", optional = true }
smol = { version = "2.0.2", optional = true }

[profile.dev]
package."*".opt-level = 3
opt-level = 1

[lints.clippy]
too-many-arguments = "allow"
type-complexity = "allow"