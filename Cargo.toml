[package]
name = "waridley_bevy_console"
version = "0.13.1"
edition = "2021"
authors = ["RichoDemus <******************>"]
homepage = "https://github.com/RichoDemus/bevy-console"
repository = "https://github.com/RichoDemus/bevy-console"
description = "dev console for bevy -- <PERSON><PERSON><PERSON>'s fork with a few small API-breaking changes"
license = "MIT"
readme = "README.md"

[dependencies]
bevy = { version = "0.16.0", default-features = false, features = ["bevy_log"] }
clap = { version = "4.5", features = ["derive", "color", "help"] }
bevy_console_derive = { path = "./bevy_console_derive", version = "0.5.0" }
bevy_egui = { version = "0.35.0", features = ["serde"] }
shlex = "1.3"
ansi-parser = "0.9"
strip-ansi-escapes = "0.2"

[dev-dependencies]
bevy = { version = "0.15" }
color-print = { version = "0.3" }

[workspace]
members = ["bevy_console_derive"]

[lints.clippy]
too-many-arguments = "allow"
