name: Release

on: 
  workflow_dispatch:
    inputs:
      dry-run:
        description: 'Run cargo publish --dry-run'
        required: true
        type: boolean
        default: true
      cargo-registry-token: 
        description: 'CARGO_REGISTRY_TOKEN'
        required: true
        type: string

jobs:
  publish:
    runs-on: windows-latest

    steps:
    - name: Mask secrets
      run: |
        $CARGO_REGISTRY_TOKEN = (jq -r '.inputs["cargo-registry-token"]' $env:GITHUB_EVENT_PATH)
        Write-Output "::add-mask::$CARGO_REGISTRY_TOKEN"
        Write-Output "CARGO_REGISTRY_TOKEN=$CARGO_REGISTRY_TOKEN" >> $env:GITHUB_ENV

    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable

    - name: Cache cargo registry
      uses: actions/cache@v2
      with:
        path: ~/.cargo/registry
        key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-registry-

    - name: Cache cargo index
      uses: actions/cache@v2
      with:
        path: ~/.cargo/git
        key: ${{ runner.os }}-cargo-index-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-index-

    - name: Run cargo publish
      if : ${{ !fromJSON(inputs.dry-run) }}
      env:
        CARGO_REGISTRY_TOKEN: ${{ env.CARGO_REGISTRY_TOKEN }}
      run: cargo publish 

    - name: Run cargo publish --dry-run
      if : ${{ fromJSON(inputs.dry-run) }}
      run: cargo publish --dry-run
