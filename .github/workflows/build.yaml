name: Check
env:
  CARGO_TERM_COLOR: always
on:
  pull_request:
  push:
    branches:
      - main
jobs:
  check:
    runs-on: ${{ matrix.os }}
    env:
      RUSTFLAGS: -D warnings
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install alsa and udev
        if: matrix.os == 'ubuntu-latest'
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev
      - name: Load cargo cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-
      - name: Check
        run: cargo check --examples --workspace
      - name: Format
        run: cargo fmt --check
      - name: Test
        run: cargo test --workspace
