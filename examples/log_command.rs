use bevy::prelude::*;
use bevy_console::{reply, AddC<PERSON><PERSON>Command, <PERSON>sol<PERSON><PERSON><PERSON><PERSON>, ConsolePlugin};
use clap::Parser;

fn main() {
    App::new()
        // set background to red
        .add_plugins((DefaultPlug<PERSON>, ConsolePlugin))
        .add_console_command::<LogCommand, _>(log_command)
        .run();
}

/// Prints given arguments to the console
#[derive(Pa<PERSON><PERSON>, ConsoleCommand)]
#[command(name = "log")]
struct LogCommand {
    /// Message to print
    msg: String,
    /// Number of times to print message
    num: Option<i64>,
}

fn log_command(mut log: ConsoleCommand<LogCommand>) {
    if let Some(Ok(LogCommand { msg, num })) = log.take() {
        let repeat_count = num.unwrap_or(1);

        for _ in 0..repeat_count {
            reply!(log, "{msg}");
        }

        log.ok();
    }
}
